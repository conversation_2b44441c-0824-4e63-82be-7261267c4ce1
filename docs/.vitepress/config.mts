import {defineConfig} from 'vitepress'

// https://vitepress.dev/reference/site-config
export default defineConfig({
    title: "NebuleMQ",
    description: "高性能MQTT消息服务",
    themeConfig: {
        // https://vitepress.dev/reference/default-theme-config
        nav: [
            {text: 'Home', link: '/'},
            {text: 'Docs', link: '/introduction'}
        ],
        sidebar: [
            {
                items: [
                    {text: '简介', link: '/introduction'},
                    {text: '快速入门', link: '/quick-start'},
                    {
                        text: '设备接入',
                        items: [
                            {text: '设备认证', link: '/device-authentication'},
                            {text: '消息基础结构', link: '/message-infrastructure'},
                            {
                                text: '设备接入协议',
                                items: [
                                    {text: '物模型', link: '/thing-model'},
                                    {text: '设备影子', link: '/device-shadow'},
                                    {text: '设备对时', link: '/ntp'},
                                    {text: '设备配置', link: '/device-config'},
                                    {text: '设备诊断', link: '/device-diagnostics'},
                                    {text: '设备日志', link: '/device-logging'},
                                    {text: '固件更新', link: '/ota'},
                                ]
                            },
                        ]
                    },
                    {
                        text: '工具',
                        items: [
                            {text: '设备模拟器', link: '/device-simulator'}
                        ]
                    },
                    {
                        text: '应用指南',
                        items: [
                            {text: '智能家居', link: '/smart-home'},
                            {text: '智慧能源', link: '/smart-energy'}
                        ]
                    },
                    {
                        text: '参考资料',
                        items: [
                            {
                                text: 'MQTT v3.1.1 Spec',
                                link: 'http://docs.oasis-open.org/mqtt/mqtt/v3.1.1/os/mqtt-v3.1.1-os.html'
                            },
                            {
                                text: 'MQTT v5.0 Spec',
                                link: 'https://docs.oasis-open.org/mqtt/mqtt/v5.0/mqtt-v5.0.html'
                            }
                        ]
                    },
                    {text: '更新日志', link: '/changelog'},
                    {text: '临时', link: '/markdown-examples'}
                ]
            }
        ]
    }
})
