# 6.6 设备配置

通过NebuleMQ提供的远程配置功能，开发人员可在不用重启设备或中断设备运行的情况下，在线远程更新设备的系统参数、网络参数等配置信息。产品开发时根据情况选择使用，该功能有两个主题：
设备配置	发布	`config/${productId}/${deviceId}/up`
设备配置	订阅	`config/${productId}/${deviceId}/down`

method取值

| 方法名     | 功能             | 使用场景                                                 |
| ---------- | ---------------- | -------------------------------------------------------- |
| set.batch  | 批量下发配置信息 | 通常用于出厂批量进行配置信息的写入。                     |
| set.single | 对单个设备配置   | 针对单个设备进行运行参数的调整，根据需要通常个单个属性。 |



使用的流程如下

<img src="/image-20250808150029636.png" alt="image-20250808150029636" style="zoom:20%;" />

下发配置数据

```json
{
  "id": "abc123",
  "method": "set.batch",
  "params": {
        "property": {
            "deviceName": "courtyard",
            "swAutoRecolsure": 0,
            "PowerSwitch": 0,
            "KeyLock": 0
        },
        "Overvoltage": {
            "overVol_OVF": 0,
            "overVol_OV": 200,
            "overVol_OVJT": 10,
            "overVol_ORV": 20,
            "overVol_OVRT": 30
        },
        "Undervoltage": {
            "underVol_LVF": 0,
            "underVol_LV": 200,
            "underVol_LVJT": 10,
            "underVol_LRV": 20,
            "underVol_LVRT": 30
        },
        "Reclose": {
            "FinalState": 0,
            "overCur_OCT": 200,
            "overCur_OCRT": 10,
            "MiddleTimeout": 20,
            "EndTimeout": 30,
            "ReclosingNumber": 0,
            "SamplingCount": 20
        },
        "LocalTimer": [
            {
                "Once": 1,
                "Timer": "10 10 * * 1,2,7",
                "Enable": 1,
                "IsValid": 1,
                "targets": "Switch=1"
            },
            {
                "Once": 0,
                "Timer": "10 10 * * 1,2,7",
                "Enable": 1,
                "IsValid": 1,
                "targets": "Switch=1"
            },
            {
                "Once": 0,
                "Timer": "10 10 * * 1,2,7",
                "Enable": 1,
                "IsValid": 1,
                "targets": "Switch=1"
            },
            {
                "Once": 0,
                "Timer": "10 10 * * 1,2,7",
                "Enable": 1,
                "IsValid": 1,
                "targets": "Switch=1"
            },
            {
                "Once": 0,
                "Timer": "10 10 * * 1,2,7",
                "Enable": 1,
                "IsValid": 1,
                "targets": "Switch=1"
            }
        ]
    },
    "Overcurrent": {
        "overCur_OCV_0": 0,
        "overCur_OCV_1": 22,
        "overCur_OCV_2": 40
    },
    "SeasonProfile": {
        "SeasonProfile1": {
            "WeakProfile": 1,
            "Timer": "10 1 8 0"
        },
        "SeasonProfile2": {
            "WeakProfile": 2,
            "Timer": "10 1 8 0"
        },
        "SeasonProfile3": {
            "WeakProfile": 0,
            "Timer": "10 1 8 0"
        },
        "SeasonProfile4": {
            "WeakProfile": 1,
            "Timer": "10 1 8 0"
        }
    },
    "DayProfile": {
        "DayProfile0": {
            "overCurStart": 1,
            "StartTimer": "10 0",
            "overCurEnd": 0,
            "EndTimer": "21 0"
        },
        "DayProfile1": {
            "overCurStart": 1,
            "StartTimer": "10 0",
            "overCurEnd": 0,
            "EndTimer": "21 0"
        },
        "DayProfile2": {
            "overCurStart": 1,
            "StartTimer": "10 0",
            "overCurEnd": 0,
            "EndTimer": "21 0"
        }
    },
    "WeakProfile": {
        "WeakProfile0": [
            0,
            0,
            0,
            0,
            2,
            0,
            0
        ],
        "WeakProfile1": [
            0,
            0,
            0,
            0,
            2,
            0,
            0
        ],
        "WeakProfile2": [
            0,
            0,
            0,
            0,
            2,
            0,
            0
        ],
        "WeakProfile3": [
            0,
            0,
            0,
            0,
            2,
            0,
            0
        ]
    }
}
```



上报配置回复消息

成功消息

```json
{
    "id":"abc123",
    "method": "set.batch",
    "code":0,
    "data":{
    }
}
```

失败消息

```json
{
    "id":"abc123",
    "method": "set.batch",
    "code":4321,
    "data":{}
}
```

