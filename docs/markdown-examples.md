# NebuleMQ设备接入协议



**文档版本：** 0.5.5
**更新日期：** \[2025-08-15]
**修订历史：**

|版本 |日期 |修订人 |修订说明 |
|:---|:---|:---|:---|
|0.5.5 |   |   |   |





# 1.概述



## 1.1 文档目的

- 定义设备如何通过安全、可靠地连接使用NebuleMQ。
- 规范设备与 NebuleMQ之间消息的主题、格式、内容、服务质量（QoS）、行为等。
- 确保不同设备实现的一致性、互操作性和可维护性。
- 为设备开发者、系统集成商和运维人员提供明确的交互指南。



## 1.2 适用范围

- 适用于所有需要接入到 NebuleMQ的各类直连设备、网关及子设备。
- 规范了设备与NebuleMQ之间的交互。



## 1.3 术语定义

- **NebuleMQ:** 一个高性能的MQTT中间件。
- **MQTT:** Message Queuing Telemetry Transport，消息队列遥测传输协议。
- **Broker:** MQTT 服务器，负责消息的路由、分发和存储。
- **Client:** 连接到 Broker 的设备或应用程序。
- **Topic:** 消息发布的频道或地址。
- **Payload:** 消息的有效载荷（内容）。
- **QoS:** Quality of Service，消息服务质量等级（0，1，2）。
- **Retained Message:** 保留消息（Broker 为 Topic 保存的最新一条消息）。
- **Last Will and Testament (LWT):** 遗嘱消息（设备非正常断开时 Broker 代为发布的消息）。
- **Client ID:** 设备在 Broker 上的唯一标识符。
- **Keep Alive:** 心跳间隔时间（秒）。



## 1.4 参考文档

- [MQTT Version v 5.0 Specification](https://docs.oasis-open.org/mqtt/mqtt/v5.0/mqtt-v5.0.html)
- [MQTT Version v3.1.1 Specification](http://docs.oasis-open.org/mqtt/mqtt/v3.1.1/os/mqtt-v3.1.1-os.html)





# 2.连接认证



## 2.1 连接信息

- **Broker 地址：** `mqtt://[客户平台域名].com` (例: `mqtt://nebulemq.com`)

MQTT 连接可以使用 TCP 、TLS/SSL两种方式，对应端口如下：

| 协议    | 端口 | 描述                                               |
| ------- | ---- | -------------------------------------------------- |
| TCP     | 1883 | 非加密 MQTT 连接(**测试开发使用，生产环境不推荐**) |
| TLS/SSL | 8883 | 基于 TLS 加密的 MQTT 连接(**生产环境强制要求**)    |



## 2.2 接入方式介绍

NebuleMQ平台需要每个接入的设备必须拥有平台为其分配的产品唯一标识Product ID，用户在创建产品时需要选择设备认证方式:密钥认证或证书认证，在设备接入时需要根据指定的方式上报产品、设备信息与对应的密钥信息，认证通过后设备才能成功连接NebuleMQ平台。

平台为保证设备的安全性，仅提供“一机一密”的鉴权认证方式，即强制要求每一个接入平台的设备，拥有平台颁发的唯一设备身份标识Device ID及设备密钥Device Secret，设备名称Device Name 可用户自定义，但需要保证全局唯一，建议使用MAC地址或IMEI码等设备唯一身份信息来保证其信息的合法性。由于不同用户的设备端资源、安全等级要求都不同，平台提供了两种认证模式(当前仅支持设备密钥的方式)，以满足不同的使用场景。

平台为您提供以下三种认证方案：

证书认证：为每台设备分配证书 + 私钥，使用非对称加密认证接入，平台会为每台设备烧录不同的配置信息提供烧录文件。

密钥认证：为每台设备分配设备密钥，使用对称加密认证接入，平台会为每台设备烧录不同的配置信息提供烧录文件。

动态注册认证：为同一产品下的所有设备出厂时仅烧写product_id和product_secret分配统一密钥，设备通过API注册请求获取设备证书/密钥后认证接入平台。

三种方案在易用性、安全性和对设备资源要求上各有优劣，您可以根据自己的业务场景综合评估选择。方案对比如下：

|特性 |证书认证 |密钥认证 |动态注册认证 |
|---|---|---|---|
|设备烧录信息 |product_id、device_name、设备证书、设备私钥<br />根证书 |product_id、device_id、device_secret |product_id、device_name、<br />product_secret(用于签名使用，网络无须传输) |
|是否需要提前创建设备 |必须 |必须 |支持根据注册请求中携带的 product_id、device_name 自动创建 |
|安全性 |高 |一般 |一般 |
|设备资源要求 |较高，需要支持 TLS |较低 |较低，支持 AES 即可 |



## 2.3 设备密钥认证



### 2.3.1 获取 MQTT 连接信息

在设备详情页，连接信息栏目，查看连接信息，包含device_id 和 device_secret 或证书。



### 2.3.2 生成 MQTT 用户名及密码

若创建产品时选的密钥认证，则需通过拼接获得 MQTT 连接用户名，并通过加密算法生成 MQTT 连接所需密码。

具体拼接及计算方式如下。

**client_id :** 

device_id(使用平台颁发设备唯一ID)



**username：**

用户名拼接方式为 `{product_id}|{device_id}|{timestamp}|{algorithm_type}`，其中：

| 字段           | 说明                                                      |
| -------------- | --------------------------------------------------------- |
| product_id     | 产品ID                                                    |
| device_id      | 设备ID                                                    |
| timestamp      | 生成签名时的时间戳，以秒为单位，长整数。可以不用传入。    |
| algorithm_type | 字符串签名算法类型,取值 MD5 或 SHA256，不传入则默认为 MD5 |



**password：**

步骤一、组合加密字符串：`{product_id}|{device_id}|{timestamp}|{algorithm_type}{device_secret}`，其中：

| 字段           | 说明                                                         |
| -------------- | ------------------------------------------------------------ |
| product_id     | 产品ID                                                       |
| device_id      | 设备ID                                                       |
| timestamp      | 生成签名时的时间戳，以秒为单位，长整数。<br />时间戳会与服务器时间判比，最大误差允许10min；<br />若无法保证时间准确性，计算时需传入0进行占位 |
| algorithm_type | 字符串签名算法类型,取值 MD5 或 SHA256                        |
| device_secret  | 平台提供设备密钥                                             |



步骤二、进行加密

使用MD5或者SHA256，对加密字符串进行加密。具体过程如下：

获取加密字符串的UTF-8字符集比特数组，按选定的加密方式，对1中得到比特数组使用MD5或者SHA256进行加密，并将**结果转换为小写**形式。





## 2.4 设备证书认证

待补全！



## 2.5 连接参数

- **Clean Session:** `true` (建议。除非设备有可靠存储并能处理离线消息，否则选 `true` 避免堆积)。
- **Keep Alive:** `60-600` 秒 (根据设备功耗和网络状况设定)。
- **LWT (遗嘱消息):**
  
  - **Topic:** `status/${productId}/${deviceName}/disconnect`
  - **Payload :**
  
    ```json
    {
        "id": "abc123",
        "params": {
            "status": "offline"
        },
        "method": "status.post"
    }
    ```
  - **QoS:** `1`
  - **Retain:** `true` (关键！确保设备离线后平台能立即获取状态)。



# 3.主题规范



## 3.1 基础结构

平台采用固定的四级结构设计MQTT主题，基础结构如下：

`{category_name}/{product_id}/{device_id}/{topic_name}`



## 3.2 系统内置主题(物模型)

|功能类型 |方向 (设备视角) |Topic 格式 |描述 |
|:---|:---|:---|:---|
|物模型 |设备 -> 平台 |`property/${product_id}/${device_id}/up` |上报设备属性值 |
|物模型 |平台 -> 设备 |`property/${product_id}/${device_id}/down` |属性下发与属性上报响应 |
|物模型 |设备 -> 平台 |`event/${product_id}/${device_id}/up` |事件上报 |
|物模型 |平台 -> 设备 |`event/${product_id}/${device_id}/down` |事件上报响应 |
|物模型 |设备 -> 平台 |`action/${product_id}/${device_id}/up` |设备响应行为执行结果 |
|物模型 |平台 -> 设备 |`action/${product_id}/${device_id}/down` |应用调用设备行为 |



## 3.3 系统内置主题(设备影子)

|功能类型 |方向 (设备视角) |Topic 格式 |描述 |
|:---|:---|:---|:---|
|设备影子 |平台 -> 设备 |`shadow/${product_id}/${device_id}/desired` |下发设备期望值 |
|设备影子 |设备 -> 平台 |`shadow/${product_id}/${device_id}/reported` |上报设备状态值 |



## 3.4 系统内置主题(设备更新)

|功能类型 |方向 (设备视角) |Topic 格式 |描述 |
|:---|:---|:---|:---|
|固件更新 |设备 -> 平台 |`ota/${product_id}/${device_id}/inform` |远程更新 |
|固件更新 |平台 -> 设备 |`ota/${product_id}/${device_id}/upgrade` |更新通知 |
|固件更新 |设备 -> 平台 |`ota/${product_id}/${device_id}/progress` |设备上报升级进度 |
|固件更新 |平台 <-> 设备 |`ota/${product_id}/${device_id}/get` |设备请求OTA升级包信息 |
|固件更新 |平台 <-> 设备 |`ota/${product_id}/${device_id}/download` |设备请求下载文件分片 |



## 3.5 系统内置主题(设备对时)

| 功能类型 | 方向 (设备视角) | Topic 格式                            | 描述                 |
| :------- | :-------------- | :------------------------------------ | :------------------- |
| 设备对时 | 设备 -> 平台    | `ntp/${product_id}/${device_id}/up`   | 设备发起对时请求     |
| 设备对时 | 平台 -> 设备    | `ntp/${product_id}/${device_id}/down` | 设备收到云端对时消息 |



## 3.6 系统内置主题(设备配置)

| 功能类型 | 方向 (设备视角) | Topic 格式                               | 描述                         |
| :------- | :-------------- | :--------------------------------------- | :--------------------------- |
| 设备配置 | 设备 -> 平台    | `config/${product_id}/${device_id}/up`   | 上报当前的配置信息           |
| 设备配置 | 平台 -> 设备    | `config/${product_id}/${device_id}/down` | 接收控制端通过云端的配置信息 |



## 3.7 系统内置主题(设备诊断)

| 功能类型 | 方向 (设备视角) | Topic 格式                             | 描述                       |
| :------- | :-------------- | :------------------------------------- | :------------------------- |
| 设备诊断 | 设备 -> 平台    | `diag/${product_id}/${device_id}/up`   | 设备主动上报网络等检测状态 |
| 设备诊断 | 平台 -> 设备    | `diag/${product_id}/${device_id}/down` | 接收设备检测指令           |



## 3.8 系统内置主题(设备日志)

| 功能类型 | 方向 (设备视角) | Topic 格式                            | 描述         |
| :------- | :-------------- | :------------------------------------ | :----------- |
| 设备日志 | 设备 -> 平台    | `log/${product_id}/${device_id}/up`   | 上报日志内容 |
| 设备日志 | 平台 -> 设备    | `log/${product_id}/${device_id}/down` | 接收日志配置 |



## 3.9 系统内置主题(设备上线离线)

|功能类型 |方向 (设备视角) |Topic 格式 |描述 |
|:---|:---|:---|:---|
|设备上线 |平台 -> 设备 |`status/${product_id}/${device_id}/connect` |下发设备期望值 |
|设备离线 |设备 -> 平台 |`status/${product_id}/${device_id}/disconnect` |上报设备状态值 |



## 3.10 自定义主题

`user/${product_id}/${device_id}/${topic_name}`

说明：

自定义主题{category_name}始终为user





# 4.数据规范



## 4.1 通用请求格式 (设备上报 / 服务响应)

```json
{
    "id": "1234567890", // 唯一消息ID (建议UUID或时间戳+序列号)，平台响应/ACK会带回此ID
    "method": "event.{event_identifier}", // String类型，标识操作类型 (见下表)
    "params": { ... },  // Object类型，具体参数内容，由物模型定义决定
}
```



## 4.2 通用响应格式 (平台对设备请求的ACK / 服务调用指令)

```json
{
    "id": "1234567890", // String类型，**必须与对应请求的`id`一致**
    "method": "event.{event_identifier}" // String类型 (服务指令特有)，标识服务类型
    "code": 0,        // Int类型，响应码 (0表示成功，其他见错误码表)
    "data": { ... },    // Object类型，响应数据内容
}
```



## 4.3 关键 `method` 值对照表

|操作类型 |`method` 值 (示例) |方向 |说明 |
|:---|:---|:---|:---|
|**获取属性值** |`property.get` |平台 -> 设备 |请求上报属性 |
|**属性主动上报** |`property.report` |设备 -> 平台 |主动上报一组属性值 |
|**设置属性值** |`property.set` |平台 -> 设备 |平台设置设备属性 |
|**事件上报** |`event.{event_identifier}` |设备 -> 平台 |上报一个特定事件 |
|**服务调用指令** |`action.{action_identifier}` |平台 -> 设备 |平台要求设备执行服务 |
|**设置影子期望值** |desired.set |平台 -> 设备 |设置影子期望值 |
|**获取影子期望值** |desired.get |设备 -> 平台 |获取影子期望值 |



## 4.4 `params` / `data` 内容规则

- **属性 (Property):** `params` 中为键值对，键是属性标识符 (Identifier)，值是其对应的数据值 (符合TSL定义的数据类型和范围)。
  - *属性上报示例 (*`params`):

    ```json
    "params": {
        "Temperature": 25.6,
        "Humidity": 45,
        "PowerSwitch": 1
    }
    ```
- **事件 (Event):** `params` 中包含 `value` 对象，其内部键是事件输出参数的标识符。
  - *事件上报示例 (*`params`):

    ```json
    "params": {
        "value": {
            "ErrorCode": 1001,
            "ErrorMsg": "Sensor failure"
        }
    }
    ```
- **服务 (Action):**
  
  - **指令 (**`method` 为 `action.{action_identifier}`): `data` 对象包含服务输入参数的键值对。
  
    ```json
    "data": {
        "TargetTemperature": 22.0
    }
    ```
  - **响应 (**`method` 为 `action.{action_identifier}`): `data` 对象包含服务输出参数的键值对 (服务定义中有输出时)。
  
    ```json
    "data": {
        "ActualTemperature": 21.8
    }
    ```



# 5.核心交互流程



![image-20250808161010657](/image-20250808161010657.png)





# 6.设备交互协议



## 6.1 物模型



### 6.1.1 概念

物模型（Thing Model）在平台中指对物理设备的数字模型，是实现物理设备与数字世界的互联互通的统一的 “数字说明书”，它是由设备的属性、服务、事件等组成的一种结构化描述。

从技术本质来看，物模型采取JSON格式描述。通过抽象设备的属性（如温度、开关状态）、方法（如启动、调节参数）、事件（如故障报警、任务完成），构建出可被程序识别的数字模型。例如，一个智能温湿度传感器的物模型会包含 “temperature”（温度属性，单位℃）、“humidity”（湿度属性，单位 %）、“report_data”（数据上报方法）、“low_battery”（低电量事件）等要素。



### 6.1.2 作用

物模型设计有以下几个作用：

1. 提高开发效率：物模型设计可以将不同设备之间的通信和数据交换规范化，减少开发人员的工作量。
2. 提高系统的可扩展性：物模型设计可以在系统中引入新的设备类型，对于不同的设备类型，可以快速实现数据的交换和管理。
3. 提高系统的可维护性：物模型设计可以将设备的特征和行为进行抽象和规范化，使得系统的架构更加清晰明了，方便维护和升级。



### 6.1.3 核心构成

● 属性（Property）：作为设备实时状态的直观呈现，用户根据企业内产品尽可能采用统一命名。例如所有设备的开关状态均用 “power” 表示，取值为布尔值 true/false，这种标准化命名极大地提升了设备间的兼容性与用户操作的便捷性。

● 方法（Action）：物模型中的方法旨在封装复杂操作逻辑，以提升设备控制的效率与便捷性。所有方法的参数都与已定义的属性紧密关联，确保操作与设备状态的实时同步。

● 事件（Event）：事件是设备主动向外界上报自身瞬时状态变化的关键途径，所有事件参数均需引用已定义属性，以保证信息的准确性与可追溯性。



### 6.1.4 属性



#### 6.1.4.1 控制端请求设备上报最新属性

**流程**

```
设备订阅
云端下发property/${productId}/${deviceId}/down  get
设备端回复属性property/${productId}/${deviceId}/up   reply
```



method值

| 主题方向 |                 |                |                            |
| -------- | --------------- | -------------- | -------------------------- |
| down     | property.get    | 获取属性值     |                            |
| up       | property.get    | 获取属性值回复 | 业务端查询后，记录最新值   |
| down     | property.set    | 设置属性值     |                            |
| up       | property.set    | 设置属性值回复 |                            |
| up       | property.report | 设备上报属性值 | 设备状态变化时，记录最新值 |
|          |                 |                |                            |

注：以上所有消息均按需（产品规则编排中）存放至DB，供查看数据使用，默认最多存放60天



**请求数据格式**：

```json
{
    "version": "1.0",
    "id": "abc123",
    "method": "property.get",
    "params": {
        "fields": [
            "mac",
            "variableVoltageA",
            "variableCurA",
            "variableFreq",
            "variableInsPT",
            "variablePwrFT",
            "variableInsST",
            "energyEpT",
            "FaultStatus",
            "Reclose"
        ]
    }
}
```



**回复数据格式**：

*成功返回*

```json
{
    "id": "abc123",
    "code": 0,
    "method": "property.get",
    "data": {
        "mac": "C2A82A05BF51",
        "variableVoltageA": 220.1,
        "variableCurA": 5.67,
        "variableFreq": 49.9,
        "variableInsPT": 69,
        "variablePwrFT": 0.85,
        "variableInsST": 256,
        "energyEpT": 9763,
        "FaultStatus": 0,
        "Reclose": {
            "FinalState": 0,
            "overCur_OCT": 200,
            "overCur_OCRT": 10,
            "MiddleTimeout": 20,
            "EndTimeout": 30,
            "ReclosingNumber": 0,
            "SamplingCount": 20
        }
    }
}
```



*失败返回*

```json
{
    "id": "abc123",
    "code": 4321,
    "method": "property.get",
    "data": {}
}
```



#### 6.1.4.2 设备端属性变化主动上报

流程

```
设备状态变化后立即上报
设备端回复属性property/${productId}/${deviceId}/up   report
平台端回复 property/${productId}/${deviceId}/down   reply
```

上报数据格式

```json
{
  "id": "abc123",
  "method":"property.report",
  "data": {
      "mac": "C2A82A05BF51",
      "variableVoltageA": 220.1,
      "variableCurA": 5.67,
      "variableFreq": 49.9,
      "variableInsPT": 69,
      "variablePwrFT": 0.85,
      "variableInsST": 256,
      "energyEpT": 9763,
      "FaultStatus": 0,      
      "reclosureStatus": 4
  }
}
```

平台回复数据格式：

成功

```json
{
    "id": "abc123",
    "code": 0,
    "method": "property.report",
    "data": {},
}
```

失败

```json
{
    "id": "abc123",
    "code": 4321,
    "method": "property.report",
    "data": {}
}
```



#### 6.1.4.3 平台端下发属性设置

**流程**

```
设备订阅
云端下发property/${productId}/${deviceId}/down
设备端回复属性property/${productId}/${deviceId}/up
```

**下发格式**

```json
{
    "id": "abc123",
    "method": "property.set",
    "params": {
        "mac": "C2A82A05BF51",
        "variableVoltageA": 220.1,
        "variableCurA": 5.67,
        "variableFreq": 49.9,
        "variableInsPT": 69,
        "variablePwrFT": 0.85,
        "variableInsST": 256,
        "energyEpT": 9763,
        "FaultStatus": 0,
        "Reclose": {
            "FinalState": 0,
            "overCur_OCT": 200,
            "overCur_OCRT": 10,
            "MiddleTimeout": 20,
            "EndTimeout": 30,
            "ReclosingNumber": 0,
            "SamplingCount": 20
        }
    }
}
```



**设备回复数据格式**

*成功*

```json
{
  "id": "abc123",
  "code": 0,
  "method": "property.set"
  "data": {
    "max_current": 20,
    "auto_shutdown": true
  }
}
```

*失败*

```json
{
    "id": "abc123",
    "code": 6813,
    "method": "property.set",
    "data": {}
}
```



### 6.1.5 事件

流程

```
流程图
```

上报消息数据格式

```json
{
    "id": "abc123",
    "method": "event.${event.identifier}",
    "ack": 0,
    "params": {
        "Power": "on",
        "WF": "2"
    }
}
```

回复数据格式

成功

```json
{
    "id": "abc123",
    "code": 0,
    "method": "event.${event.identifier}",
    "data": {}
}
```

失败

```json
{
    "id": "abc123",
    "code": 4321,
    "method": "event.${event.identifier}",
    "data": {}
}
```



### 6.1.6 行为

**流程**

```
```

发下数据格式

```json
{
    "id": "abc123",
    "method": "action.${action.identifier}"
    "params": {
        "Power": "on",
        "WF": "2"
    },
}
```

回复数据格式

成功

```json
{
    "id": "abd123",
    "method": "action.${action.identifier}",
    "code": 0,
    "data": {}
}
```

失败

```json
{
    "id": "abd123",
    "method": "action.${action.identifier}",
    "code": 4321,
    "data": {}
}
```



## 6.2 设备影子



### 6.2.1 概念

设备影子(Device Shadow)是设备在云端的虚拟映射，用来记录设备的最近状态和预期状态，通过设备影子可以轻松实现云端对设备状态的管控。设备影子是一个JSON文档，存储了设备的当前状态、属性和配置信息，使得即使设备处于离线状态或无法直接访问时，应用程序和服务仍然能够与之进行交互。每个设备都拥有且仅拥有一个独特的设备影子，由设备ID进行唯一标识，设备可以通过MQTT获取和设置设备影子来同步状态，该同步可以是影子同步给设备，也可以是设备同步给影子。



### 6.2.2 设备影子的组成

设备影子采用json格式存储，最外层只能包含四个字段，分别是reported、desired、timestamp、version。

| 属性          | 描述                                                         |      |
| ------------- | ------------------------------------------------------------ | ---- |
| **reported**  | 设备的报告状态。设备可以在reported部分写入数据，报告其最新状态。应用程序可以通过读取该参数值，获取设备的状态。JSON文档中也可以不包含reported部分，没有reported部分的文档同样为有效影子JSON文档。 |      |
| **desired**   | 设备的预期状态。仅当设备影子文档具有预期状态时，才包含desired部分。应用程序向desired部分写入数据，更新事物的状态，而无需直接连接到该设备。 |      |
| **timestamp** | 影子文档的最新更新时间。                                     |      |
| **version**   | 用户主动更新版本号时，设备影子会检查请求中的**version**值是否大于当前版本号。如果大于当前版本号，则更新设备影子，并将**version**值更新到请求的版本中，反之则会拒绝更新设备影子。该参数更新后，版本号会递增，用于确保正在更新的文档为最新版本。version参数为long型。为防止参数溢出，您可以手动传入`-1`将版本号重置为`0`。 |      |

其限制条件如下：

- 属性名称不能以@开头，且其中不能包含dollar（$），dot（.），space（ ），以及comma（,）符号，<=64bytes
- 属性值支持bool、int32、int64、float、double、string，不支持null和array，对于string类型<=512bytes
- 对于reported，desired每个存储体嵌套层级<=5
- 每个JsonObject层级中key值<=50



### 6.2.3 工作原理

设备影子通过MQTT等协议与设备进行双向通信，实现了状态的同步与更新。当设备状态发生变化时，它会通过MQTT将最新的状态信息上报给云端，并更新到设备影子中。同样，当应用程序需要修改设备状态时，它会将新的状态信息写入设备影子，并等待设备上线后同步更新。



### 6.2.4 使用场景

#### 场景1：网络不稳定，设备频繁上下线。

由于网络不稳定，设备频繁上下线。应用程序发出需要获取当前的设备状态请求时，设备掉线，无法获取设备状态，但下一秒设备又连接成功，应用程序无法正确发起请求。

使用设备影子机制存储设备最新状态，一旦设备状态产生变化，设备会将状态同步到设备影子。应用程序只需要请求或订阅推送方式获取影子中的状态即可，不需要关心设备是否在线。

#### 场景2：多程序同时请求获取设备状态。

如果设备网络稳定，很多应用程序请求获取设备状态，设备需要根据请求响应多次，即使响应的结果是一样的，设备本身处理能力有限，无法负载被请求多次的情况。

使用设备影子机制，设备只需要主动同步状态给设备影子一次，多个应用程序请求或订阅推送方式，获取设备影子中存储的设备状态，即可获取设备最新状态，做到应用程序和设备的解耦。

#### 场景3：设备掉线。

设备网络不稳定，导致设备频繁上下线，应用程序发送控制指令给设备时，设备掉线，指令无法下达到设备。通过QoS=1或者2实现，但是该方法对于服务端的压力比较大，一般不建议使用。

使用设备影子机制，应用程序发送控制指令，指令携带时间戳保存在设备影子中。当设备掉线重连时，获取指令并根据时间戳确定是否执行。

设备连接掉线，指令发送失败。设备再上线时，设备影子功能通过指令加时间戳的模式，保证设备不会执行过期指令。

#### 场景4：设备状态比对通知

设备上报状态时，仅需报告变更的部分；此时应用对发生变化的属性值更为关心。反之应用对设备亦然。

应用或设备更新属性（desired/reported）后，设备或应用可获取差异推送delta。

#### 场景5：设备初始配置信息获取

设备首次连接时，需要一些配置项或参数作为初始化配置，一般可将配置信息写入固件，但具有差异化的配置就较难处理。

使用设备影子机制，可以将一般性的配置写入影子模板，以此模板创建设备时模板内容将作为设备初始版本的影子。若针对特定设备变更初始配置，也可以针对性更新其影子，设备首次连接时进行 get 获取影子，即可获取期望配置。



### 6.2.5 设备端 



#### 6.2.5.1 设备影子主题

平台已为每个设备内置了两个系统Topic，用于实现设备影子数据流转。

> `shadow/${productId}/${deviceId}/desired`

设备订阅此Topic获取最新期望值消息，应用程序发布消息到此Topic后，如果设备在线通过该Topic能立即收到消息，不论是否在线进入该Topic的消息后，均会将消息中的期望值更新到设备影子中。

>`shadow/${productId}/${deviceId}/reported`

设备影子更新状态到该Topic，平台收到数据后会将消息更新影子的reported区中。

方法类别

|                 |                    |                                    |
| --------------- | ------------------ | ---------------------------------- |
| desired.set     | 设置期望值         | 设备在线                           |
| desired.get     | 设备主动获取期望值 | 设备上线后发送该指令获取影子期望值 |
| shadow.reported | 上报属性值         |                                    |



#### 6.2.5.2 设备收到期望值设置

流程图



设置数据

```json
{
    "id":"abc123",
    "method":"desired.set",
    "data":{
        "power":1,
        "temperature":28.1
    }
}
```



消息回复

成功

```json
{
    "id":"abc123",
    "method":"desired.set",
    "code":0,
    "data":{}
}
```



失败

```json
{
    "id":"abc123",
    "method":"desired.set",
    "code":4321,
    "data":{}
}
```





#### 6.2.5.3 主动获取设备期望值

流程图



数据上行

```json
{
    "id" : "abc123",
    "method":"desired.get",
    "params" : [
        "power",
        "temperature"
    ]
}
```

数据回报

成功消息

```json
{
    "id":"abc123",
    "method":"desired.get",
    "code":0,
    "data":{
        "power":1,
        "temperature":28.1
    }
}
```

失败消息 

```json
{
    "id":"abc123",
    "method":"shadow.get",
    "code":4321,
    "data":{}
}
```



#### 6.2.5.4 上报设备属性值

流程图



数据上行

```json
{
    "id" : "abc123",
    "method":"shadow.reported",
    "params" : {
        "power":true,
        "temperature":23.6
    }
}
```

数据回报

成功消息

```json
{
    "id":"abc123",
    "method":"shadow.reported",
    "code":0,
    "data":{
    }
}
```

失败消息

```json
{
    "id":"abc123",
    "method":"shadow.reported",
    "code":4321,
    "data":{}
}
```







### 6.2.6 应用服务接口



#### 6.2.6.1 配置设备影子期望数据

云端可以调用SetShadowDesired接口，设置期望属性值（desired区）来控制设备。在云端设置设备期望属性值后，若设备在线，将实时更新设备属性状态；若设备离线，期望属性值将缓存云端，待设备上线后，获取期望属性值，并更新属性状态。



#### 6.2.6.2 查询影子数据

应用服务器可调用此接口查询指定设备的设备影子信息，包括对设备的期望属性信息（desired区）和设备最新上报的属性信息（reported区）



#### 6.2.6.3 删除设备影子

用户删除设备影子，平台将会将设备影子中的所有数据（包含上报值和期望值）清空。





## 6.3 固件更新(OTA)

OTA（Over-the-Air Technology）即空中下载技术，基于无线网络对设备固件、软件或驱动进行更新。通过OTA升级，可以对物联网设备更新功能、修复漏洞、优化性能。



### 6.3.1 升级流程

<img src="/image-20250808145619043.png" alt="image-20250808145619043" style="zoom:20%;" />





```json
{
  "id": "abc123",
  "method": "set",
  "params": {
        "fileSign":"93230c3bde425a9d7984a594ac56****",
        "size": 278421,
        "version": "v1.0.32",
        "url": "https://the_firmware_url",
        "signMethod": "MD5",
        "md5": "f8d85b250d4d787a9f48***",
        "module": "wifi",
        "extData":{
            "key1":"value1",
            "key2":"value2"
        }
    }
}
```







## 6.4 透传

针对资源受限设备使用场景，平台也支持二进制数据完成数据的上报和下发。

使用二进制格式进行编码可以解决JSON格式的序列化开销大，生成报文长等问题。





## 6.5 设备NTP服务

​        设备NTP 服务主要是解决资源受限的设备，系统不包含 NTP 服务，没有精确时间戳的问题。
系统内置两个 Topic来实现NTP服务：

设备对时	发布	ntp/${productId}/${deviceId}/up
设备对时	订阅	ntp/${productId}/${deviceId}/down

实现原理
物联网通信平台借鉴 NTP 协议原理，将平台作为 NTP 服务器。设备端向平台请求时，平台返回的 NTP 时间。设备端收到返回后，再结合请求时间和接收时间，一起计算出当前精确时间。
操作步骤见下图：

流程

```
```

NTP服务使用流程，及其Topic说明如下：


1. 设备端向Topic：`ntp/${productId}/${deviceId}/up`发送一条QoS=0的消息，携带设备当前的时间戳，单位为毫秒。示例如下：

   ```json
   {
       "id": "abc123",
       "method": "set",
       "params": {
           "deviceSendTime": "1571724098000"
       }
   }
   ```
   
   **说明**
   
   - 时间戳数字，支持Long（默认）和String类型。
   - NTP服务目前仅支持QoS=0的消息。
2. 设备端通过Topic：`ntp/${productId}/${deviceId}/down`，收到物联网平台回复的消息，包含以下信息。

   ```json
   {
       "id": "abc123",
       "method": "set.batch",
       "code": 0,
       "data": {
           "deviceSendTime": "1571724098000",
           "serverRecvTime": "1571724098110",
           "serverSendTime": "1571724098115"
       }
   }
   ```
3. 设备端计算出服务端当前精确的Unix时间。

   假设基于请求的时延和响应的时延相同，设备端收到服务端的时间即${deviceRecvTime}，则设备上的精确时间为：`(${serverRecvTime}+${serverSendTime}+${deviceRecvTime}-${deviceSendTime})/2`。



## 6.6 设备配置

通过NebuleMQ提供的远程配置功能，开发人员可在不用重启设备或中断设备运行的情况下，在线远程更新设备的系统参数、网络参数等配置信息。产品开发时根据情况选择使用，该功能有两个主题：
设备配置	发布	`config/${productId}/${deviceId}/up`
设备配置	订阅	`config/${productId}/${deviceId}/down`

method取值

| 方法名     | 功能             | 使用场景                                                 |
| ---------- | ---------------- | -------------------------------------------------------- |
| set.batch  | 批量下发配置信息 | 通常用于出厂批量进行配置信息的写入。                     |
| set.single | 对单个设备配置   | 针对单个设备进行运行参数的调整，根据需要通常个单个属性。 |



使用的流程如下

<img src="/image-20250808150029636.png" alt="image-20250808150029636" style="zoom:20%;" />

下发配置数据

```json
{
  "id": "abc123",
  "method": "set.batch",
  "params": {
        "property": {
            "deviceName": "courtyard",
            "swAutoRecolsure": 0,
            "PowerSwitch": 0,
            "KeyLock": 0
        },
        "Overvoltage": {
            "overVol_OVF": 0,
            "overVol_OV": 200,
            "overVol_OVJT": 10,
            "overVol_ORV": 20,
            "overVol_OVRT": 30
        },
        "Undervoltage": {
            "underVol_LVF": 0,
            "underVol_LV": 200,
            "underVol_LVJT": 10,
            "underVol_LRV": 20,
            "underVol_LVRT": 30
        },
        "Reclose": {
            "FinalState": 0,
            "overCur_OCT": 200,
            "overCur_OCRT": 10,
            "MiddleTimeout": 20,
            "EndTimeout": 30,
            "ReclosingNumber": 0,
            "SamplingCount": 20
        },
        "LocalTimer": [
            {
                "Once": 1,
                "Timer": "10 10 * * 1,2,7",
                "Enable": 1,
                "IsValid": 1,
                "targets": "Switch=1"
            },
            {
                "Once": 0,
                "Timer": "10 10 * * 1,2,7",
                "Enable": 1,
                "IsValid": 1,
                "targets": "Switch=1"
            },
            {
                "Once": 0,
                "Timer": "10 10 * * 1,2,7",
                "Enable": 1,
                "IsValid": 1,
                "targets": "Switch=1"
            },
            {
                "Once": 0,
                "Timer": "10 10 * * 1,2,7",
                "Enable": 1,
                "IsValid": 1,
                "targets": "Switch=1"
            },
            {
                "Once": 0,
                "Timer": "10 10 * * 1,2,7",
                "Enable": 1,
                "IsValid": 1,
                "targets": "Switch=1"
            }
        ]
    },
    "Overcurrent": {
        "overCur_OCV_0": 0,
        "overCur_OCV_1": 22,
        "overCur_OCV_2": 40
    },
    "SeasonProfile": {
        "SeasonProfile1": {
            "WeakProfile": 1,
            "Timer": "10 1 8 0"
        },
        "SeasonProfile2": {
            "WeakProfile": 2,
            "Timer": "10 1 8 0"
        },
        "SeasonProfile3": {
            "WeakProfile": 0,
            "Timer": "10 1 8 0"
        },
        "SeasonProfile4": {
            "WeakProfile": 1,
            "Timer": "10 1 8 0"
        }
    },
    "DayProfile": {
        "DayProfile0": {
            "overCurStart": 1,
            "StartTimer": "10 0",
            "overCurEnd": 0,
            "EndTimer": "21 0"
        },
        "DayProfile1": {
            "overCurStart": 1,
            "StartTimer": "10 0",
            "overCurEnd": 0,
            "EndTimer": "21 0"
        },
        "DayProfile2": {
            "overCurStart": 1,
            "StartTimer": "10 0",
            "overCurEnd": 0,
            "EndTimer": "21 0"
        }
    },
    "WeakProfile": {
        "WeakProfile0": [
            0,
            0,
            0,
            0,
            2,
            0,
            0
        ],
        "WeakProfile1": [
            0,
            0,
            0,
            0,
            2,
            0,
            0
        ],
        "WeakProfile2": [
            0,
            0,
            0,
            0,
            2,
            0,
            0
        ],
        "WeakProfile3": [
            0,
            0,
            0,
            0,
            2,
            0,
            0
        ]
    }
}
```



上报配置回复消息

成功消息

```json
{
    "id":"abc123",
    "method": "set.batch",
    "code":0,
    "data":{
    }
}
```

失败消息

```json
{
    "id":"abc123",
    "method": "set.batch",
    "code":4321,
    "data":{}
}
```





## 6.7 设备诊断

使用Wi-Fi接入网络的设备可以主动将网络状态信息和网络错误数据，通过指定Topic上报至云端。下面介绍设备上报网络状态的Topic、数据格式和网络错误数据说明。

用于功能测试，设备自检将自检结果数据通过该功能进行上报

运行过程中可以获取网络状态信息和网络错误数据等

设备主动上报网络等检测状态	发布	diag/${productId}/${deviceId}/up
接收设备检测指令	订阅	diag/${productId}/${deviceId}/down

流程

### 平台下发诊断信息给设备

```
平台下发检查指令 diag/${productId}/${deviceId}/down
```

下发消息数据格式

```json
{
  "id": "abc123",
  "method": "get.current",
  "params": {
    "diag_type": "wifi"
  }
}
```

设备上报诊断信息

```json
{
  "id": "abd123",
  "method": "get.current",
  "data": {
      "wifi": {
        "rssi": 75,
        "snr": 20,
        "per": 10,
        "err_stats":"10,02,01;10,05,01"
      }
    }
  }
}
```

设备上报历史信息

```json
{
    "id": "123",
    "method": "get.history",
    "data": {
        "wifi": [
            {
                "rssi": 75,
                "snr": 20,
                "per": 10,
                "err_stats": "10,02,01;10,05,01"
            },
            {
                "rssi": 75,
                "snr": 20,
                "per": 10,
                "err_stats": "10,02,01;10,05,01"
            }
        ]
    }
}
```

主动上报

流程

上报数据

```json
{
  "id": "abd123",
  "method": "report.current",
  "data": {
      "wifi": {
        "rssi": 75,
        "snr": 20,
        "per": 10,
        "err_stats":"10,02,01;10,05,01"
      }
    }
  }
}
```

主动上报设备历史

```json
{
    "id": "123",
    "method": "report.history",
    "data": {
        "wifi": [
            {
                "rssi": 75,
                "snr": 20,
                "per": 10,
                "err_stats": "10,02,01;10,05,01"
            },
            {
                "rssi": 75,
                "snr": 20,
                "per": 10,
                "err_stats": "10,02,01;10,05,01"
            }
        ]
    }
}
```



## 6.8 设备日志

平台支持设备本地日志的上报，您可以NebuleMQ控制台的设备日志页面，查询设备本地日志，进行故障分析。

平台支持单条日志及批量日志，可通过参数选择是否需要持久化存储。

后续根据需求考虑增加日志文件的上传功能。

